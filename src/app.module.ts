import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/user.module';
import { ProjectsModule } from './projects/projects.module';
import { TestCasesModule } from './test-cases/test-cases.module';
import { CompaniesModule } from './companies/companies.module';
import { IntegrationsModule } from './integrations/integrations.module';
import { ApiKeysModule } from './api-keys/api-keys.module';
import { TestRunModule } from './test-runs/test-run.module';
import { IssuesModule } from './issues/issues.module';
import { HealthModule } from './health/health.module';
import { DeviceFarmModule } from './device-farm/device-farm.module';
import { TempTestResultsModule } from './temp-test-results/temp-test-results.module';
import { TestResultsModule } from './test-results/test-results.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AnalyticsModule } from './analytics/analytics.module';

const synchronize = process.env.NODE_ENV !== 'production';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get<number>('DB_PORT', 5432),
        username: configService.get('DB_USERNAME', 'postgres'),
        password: configService.get('DB_PASSWORD', 'Asdqwe123@Agentq'),
        database: configService.get('DB_DATABASE', 'agentq_enterprise'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: synchronize, // Set to false in production
        logging: ['error', 'warn'],
      }),
    }),
    AuthModule,
    UsersModule,
    ProjectsModule,
    TestCasesModule,
    CompaniesModule,
    IntegrationsModule,
    ApiKeysModule,
    TestRunModule,
    IssuesModule,
    HealthModule,
    TempTestResultsModule,
    TestResultsModule,
    DeviceFarmModule,
    EventEmitterModule.forRoot(),
    AnalyticsModule
  ],
})
export class AppModule {}
